export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          phone: string | null
          role: 'user' | 'admin'
          avatar_url: string | null
          points: number
          level: string
          total_points_earned: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          phone?: string | null
          role?: 'user' | 'admin'
          avatar_url?: string | null
          points?: number
          level?: string
          total_points_earned?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          phone?: string | null
          role?: 'user' | 'admin'
          avatar_url?: string | null
          points?: number
          level?: string
          total_points_earned?: number
          created_at?: string
          updated_at?: string
        }
      }
      levels: {
        Row: {
          id: string
          name: string
          description: string | null
          min_points: number
          color: string
          icon: string | null
          rewards: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          min_points: number
          color: string
          icon?: string | null
          rewards?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          min_points?: number
          color?: string
          icon?: string | null
          rewards?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      achievements: {
        Row: {
          id: string
          name: string
          description: string
          icon: string
          type: 'check_in' | 'rental' | 'points' | 'social' | 'special' | 'streak' | 'explorer' | 'champion'
          condition: Json
          reward_points: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          icon: string
          type: 'check_in' | 'rental' | 'points' | 'social' | 'special' | 'streak' | 'explorer' | 'champion'
          condition: Json
          reward_points: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          icon?: string
          type?: 'check_in' | 'rental' | 'points' | 'social' | 'special' | 'streak' | 'explorer' | 'champion'
          condition?: Json
          reward_points?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      user_achievements: {
        Row: {
          id: string
          user_id: string
          achievement_id: string
          earned_at: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          achievement_id: string
          earned_at?: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          achievement_id?: string
          earned_at?: string
          created_at?: string
        }
      }
      tourist_spots: {
        Row: {
          id: string
          name: string
          description: string
          latitude: number
          longitude: number
          points_reward: number
          image_url: string | null
          address: string | null
          is_active: boolean
          check_in_radius: number
          category: 'tourist_spot' | 'restaurant' | 'rental_services'
          spots_whatsapp: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          latitude: number
          longitude: number
          points_reward: number
          image_url?: string | null
          address?: string | null
          is_active?: boolean
          check_in_radius?: number
          category?: 'tourist_spot' | 'restaurant' | 'rental_services'
          spots_whatsapp?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          latitude?: number
          longitude?: number
          points_reward?: number
          image_url?: string | null
          address?: string | null
          is_active?: boolean
          check_in_radius?: number
          category?: 'tourist_spot' | 'restaurant' | 'rental_services'
          spots_whatsapp?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      check_ins: {
        Row: {
          id: string
          user_id: string
          tourist_spot_id: string
          points_earned: number
          check_in_time: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          tourist_spot_id: string
          points_earned: number
          check_in_time?: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          tourist_spot_id?: string
          points_earned?: number
          check_in_time?: string
          created_at?: string
        }
      }
      point_transactions: {
        Row: {
          id: string
          user_id: string
          points: number
          type: 'earned' | 'redeemed' | 'bonus' | 'penalty'
          source: 'check_in' | 'achievement' | 'admin' | 'referral' | 'rental' | 'social'
          description: string
          metadata: Json | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          points: number
          type: 'earned' | 'redeemed' | 'bonus' | 'penalty'
          source: 'check_in' | 'achievement' | 'admin' | 'referral' | 'rental' | 'social'
          description: string
          metadata?: Json | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          points?: number
          type?: 'earned' | 'redeemed' | 'bonus' | 'penalty'
          source?: 'check_in' | 'achievement' | 'admin' | 'referral' | 'rental' | 'social'
          description?: string
          metadata?: Json | null
          created_at?: string
        }
      }
      rewards: {
        Row: {
          id: string
          name: string
          description: string
          cost_points: number
          type: 'discount' | 'free_rental' | 'merchandise' | 'experience' | 'partner_benefit'
          value: string
          image_url: string | null
          is_active: boolean
          stock: number | null
          valid_until: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          cost_points: number
          type: 'discount' | 'free_rental' | 'merchandise' | 'experience' | 'partner_benefit'
          value: string
          image_url?: string | null
          is_active?: boolean
          stock?: number | null
          valid_until?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          cost_points?: number
          type?: 'discount' | 'free_rental' | 'merchandise' | 'experience' | 'partner_benefit'
          value?: string
          image_url?: string | null
          is_active?: boolean
          stock?: number | null
          valid_until?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      reward_redemptions: {
        Row: {
          id: string
          user_id: string
          reward_id: string
          points_spent: number
          status: 'pending' | 'approved' | 'used' | 'expired'
          redeemed_at: string
          used_at: string | null
          expires_at: string | null
          approved_at: string | null
          approved_by: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          reward_id: string
          points_spent: number
          status?: 'pending' | 'approved' | 'used' | 'expired'
          redeemed_at?: string
          used_at?: string | null
          expires_at?: string | null
          approved_at?: string | null
          approved_by?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          reward_id?: string
          points_spent?: number
          status?: 'pending' | 'approved' | 'used' | 'expired'
          redeemed_at?: string
          used_at?: string | null
          expires_at?: string | null
          approved_at?: string | null
          approved_by?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      vehicles: {
        Row: {
          id: string
          name: string
          type: 'scooter' | 'bike' | 'e-bike' | 'skateboard'
          description: string | null
          image_url: string | null
          hourly_price: number
          daily_price: number | null
          is_available: boolean
          location: string | null
          vehicle_whatsapp: string | null
          features: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          type: 'scooter' | 'bike' | 'e-bike' | 'skateboard'
          description?: string | null
          image_url?: string | null
          hourly_price: number
          daily_price?: number | null
          is_available?: boolean
          location?: string | null
          vehicle_whatsapp?: string | null
          features?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          type?: 'scooter' | 'bike' | 'e-bike' | 'skateboard'
          description?: string | null
          image_url?: string | null
          hourly_price?: number
          daily_price?: number | null
          is_available?: boolean
          location?: string | null
          vehicle_whatsapp?: string | null
          features?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      app_settings: {
        Row: {
          id: string
          key: string
          value: Json
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          key: string
          value: Json
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          key?: string
          value?: Json
          description?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}