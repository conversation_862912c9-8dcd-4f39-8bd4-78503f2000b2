'use client';

import { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { createClient } from '@/utils/supabase/client';
import { Profile } from '@/types';
import { useRouter } from 'next/navigation';

export interface AuthContextType {
  user: User | null;
  profile: Profile | null;
  session: Session | null;
  loading: boolean;
  initialized: boolean;
  signIn: (email: string, password: string) => Promise<{ error?: string }>;
  signUp: (email: string, password: string, fullName: string, phone: string) => Promise<{ error?: string }>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<Profile>) => Promise<{ error?: string }>;
  refreshProfile: () => Promise<void>;
  refreshSession: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);
  const router = useRouter();

  // Create Supabase client following official Next.js documentation
  const supabase = useMemo(() => createClient(), []);

  // Fetch user profile
  const fetchProfile = useCallback(async (userId: string): Promise<Profile | null> => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        
        // If profile doesn't exist, create a basic one
        if (error.code === 'PGRST116') {
          const { data: { user: currentUser } } = await supabase.auth.getUser();
          if (currentUser) {
            const newProfile: Omit<Profile, 'id' | 'created_at' | 'updated_at'> = {
              email: currentUser.email || '',
              full_name: currentUser.user_metadata?.full_name || '',
              phone: currentUser.user_metadata?.phone || '',
              role: 'user' as const,
              avatar_url: null,
              points: 0,
              level: 'Bronze',
              total_points_earned: 0,
            };
            
            const { data: insertedProfile, error: insertError } = await supabase
              .from('profiles')
              .insert({
                id: currentUser.id,
                ...newProfile,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              })
              .select()
              .single();
              
            if (!insertError && insertedProfile) {
              return insertedProfile;
            }
          }
        }
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in fetchProfile:', error);
      return null;
    }
  }, [supabase]);

  // Initialize auth state
  useEffect(() => {
    let mounted = true;

    const getInitialSession = async () => {
      try {
        setLoading(true);
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Error getting session:', error);
          return;
        }

        if (mounted) {
          setSession(session);
          setUser(session?.user ?? null);
          
          if (session?.user) {
            const userProfile = await fetchProfile(session.user.id);
            if (mounted) {
              setProfile(userProfile);
            }
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        if (mounted) {
          setLoading(false);
          setInitialized(true);
        }
      }
    };

    getInitialSession();

    return () => {
      mounted = false;
    };
  }, [supabase, fetchProfile]);

  // Listen to auth changes - Fixed to prevent deadlocks during tab switching
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, newSession) => {
        //console.log('🔄 Auth state changed:', event, newSession?.user?.id);
        
        setSession(newSession);
        setUser(newSession?.user ?? null);
        
        if (newSession?.user) {
          // Use setTimeout to defer async operations and prevent deadlocks
          setTimeout(async () => {
            const userProfile = await fetchProfile(newSession.user.id);
            setProfile(userProfile);
          }, 0);
        } else {
          setProfile(null);
        }
        
        if (initialized) {
          setLoading(false);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [supabase, fetchProfile, initialized]);

  // Sign in function
  // const signIn = useCallback(async (email: string, password: string): Promise<{ error?: string }> => {
  //   try {
  //     setLoading(true);
      
  //     const { error } = await supabase.auth.signInWithPassword({
  //       email: email.toLowerCase().trim(),
  //       password,
  //     });

  //     if (error) {
  //       return { error: getErrorMessage(error.message) };
  //     }

  //     return {};
  //   } catch (error) {
  //     console.error('Unexpected error during sign in:', error);
  //     return { error: 'Erro inesperado ao fazer login' };
  //   } finally {
  //     setLoading(false);
  //   }
  // }, [supabase]);

// Sign in function

  // Função para adicionar bônus no primeiro login
  const addWelcomePointsIfFirstLogin = async (userId: string) => {
    // 1️⃣ Pega o total_points_earned do usuário
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('total_points_earned')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('Erro ao buscar perfil do usuário:', profileError.message);
      return;
    }

  // 2️⃣ Se total_points_earned === 0, adiciona os pontos
  if (profile.total_points_earned === 0) {
    const { error: pointsError } = await supabase.rpc('add_points_to_user', {
      user_id: userId,
      points: 100,
      description: 'Bônus de boas-vindas',
      source: 'signup' // ou 'admin', desde que exista no ENUM
    });

    if (pointsError) {
      console.error('Erro ao adicionar pontos de boas-vindas:', pointsError.message);
    } else {
      console.log('Bônus de boas-vindas adicionado!');
    }
  } else {
    console.log('Usuário já recebeu bônus anteriormente.');
  }
};


const signIn = useCallback(
  async (email: string, password: string): Promise<{ error?: string }> => {
    try {
      setLoading(true);

      const { error } = await supabase.auth.signInWithPassword({
        email: email.toLowerCase().trim(),
        password,
      });

      if (error) {
        return { error: getErrorMessage(error.message) };
      }
      supabase.auth.onAuthStateChange(async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user?.id) {
          await addWelcomePointsIfFirstLogin(session.user.id);
        }
      });

      return {};
    } catch (error) {
      console.error("Unexpected error during sign in:", error);
      return { error: "Erro inesperado ao fazer login" };
    } finally {
      setLoading(false);
    }
  },
  [supabase]
);

// Sign up function
const signUp = useCallback(async (
  email: string,
  password: string,
  fullName: string,
  phone: string,
  // cpf: string
): Promise<{ error?: string }> => {
  try {
    setLoading(true);

    const cleanEmail = email.normalize("NFKC").trim().toLowerCase();

    const { data, error } = await supabase.auth.signUp({
      email: cleanEmail,
      password,
      options: {
        data: {
          full_name: fullName,
          phone: phone,
          // cpf: cpf || null,
        },
      },
    });

    if (error) {
      console.error("Erro ao cadastrar usuário:", error.message);
      return { error: getErrorMessage(error.message) };
    }

    // console.log("Usuário criado:", data);
    return {};
  } catch (error) {
    console.error("Erro inesperado no signUp:", error);
    return { error: "Erro inesperado ao criar conta" };
  } finally {
    setLoading(false);
  }
}, [supabase]);



// Update profile function
const updateProfile = useCallback(
  async (
    updates: Partial<Profile> // recebe um objeto com campos parciais
  ): Promise<{ error?: string }> => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        return { error: "Usuário não autenticado" };
      }

      const { error } = await supabase
        .from("profiles")
        .update(updates) // passa o objeto diretamente
        .eq("id", user.id);

      if (error) {
        console.error("Erro ao atualizar perfil:", error.message);
        return { error: error.message };
      }

      return {};
    } catch (error) {
      console.error("Unexpected error during profile update:", error);
      return { error: "Erro inesperado ao atualizar perfil" };
    }
  },
  [supabase]
);




  // Sign out function
  const signOut = useCallback(async (): Promise<void> => {
    try {
      setLoading(true);
      
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error('Error signing out:', error);
      }
      
      // Clear local state
      setUser(null);
      setProfile(null);
      setSession(null);
      
      router.push('/');
    } catch (error) {
      console.error('Unexpected error during sign out:', error);
      // Still clear state and redirect
      setUser(null);
      setProfile(null);
      setSession(null);
      router.push('/');
    } finally {
      setLoading(false);
    }
  }, [supabase, router]);

  // Update profile function
  // const updateProfile = useCallback(async (updates: Partial<Profile>): Promise<{ error?: string }> => {
  //   if (!user) {
  //     return { error: 'Usuário não autenticado' };
  //   }

  //   try {
  //     const { error } = await supabase
  //       .from('profiles')
  //       .update({
  //         ...updates,
  //         updated_at: new Date().toISOString(),
  //       })
  //       .eq('id', user.id);

  //     if (error) {
  //       return { error: `Erro ao atualizar perfil: ${error.message}` };
  //     }

  //     // Refresh profile
  //     await refreshProfile();
  //     return {};
  //   } catch (error) {
  //     console.error('Unexpected error updating profile:', error);
  //     return { error: 'Erro inesperado ao atualizar perfil' };
  //   }
  // }, [user, supabase]);

  // Refresh profile function
  const refreshProfile = useCallback(async (): Promise<void> => {
    if (user) {
      const userProfile = await fetchProfile(user.id);
      setProfile(userProfile);
    }
  }, [user, fetchProfile]);

  // Refresh session function
  const refreshSession = useCallback(async (): Promise<void> => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('Error refreshing session:', error);
        return;
      }

      setSession(session);
      setUser(session?.user ?? null);
      
      if (session?.user) {
        const userProfile = await fetchProfile(session.user.id);
        setProfile(userProfile);
      } else {
        setProfile(null);
      }
    } catch (error) {
      console.error('Unexpected error refreshing session:', error);
    }
  }, [supabase, fetchProfile]);

  const value = useMemo(
    () => ({
      user,
      profile,
      session,
      loading,
      initialized,
      signIn,
      signUp,
      signOut,
      updateProfile,
      refreshProfile,
      refreshSession,
    }),
    [
      user,
      profile,
      session,
      loading,
      initialized,
      signIn,
      signUp,
      signOut,
      updateProfile,
      refreshProfile,
      refreshSession,
    ]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Error message mapping
function getErrorMessage(error: string): string {
  const errorMessages: Record<string, string> = {
    'Invalid login credentials': 'Email ou senha incorretos',
    'Email not confirmed': 'Email não confirmado. Verifique sua caixa de entrada',
    'User already registered': 'Este email já está cadastrado',
    'Password should be at least 6 characters': 'A senha deve ter pelo menos 6 caracteres',
    'Invalid email format': 'Formato de email inválido',
    'Too many requests': 'Muitas tentativas. Tente novamente em alguns minutos',
    'Email rate limit exceeded': 'Limite de emails excedido. Tente novamente mais tarde',
    'Email address not confirmed': 'Email não confirmado. Verifique sua caixa de entrada',
    'Invalid email': 'Email inválido',
    'Signup requires a valid password': 'É necessária uma senha válida para o cadastro',
    'Unable to validate email address: invalid format': 'Formato de email inválido',
    'Database connection lost': 'Conexão com o banco de dados perdida. Tente novamente',
    'Network request failed': 'Falha na conexão. Verifique sua internet',
    'Session not found': 'Sessão expirada. Faça login novamente',
    'JWT expired': 'Sessão expirada. Faça login novamente',
  };

  return errorMessages[error] || 'Ocorreu um erro inesperado. Tente novamente';
}