'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { X, MapPin, Utensils, Car } from 'lucide-react';
import { TouristSpotCategory } from '@/types';

interface CreateTouristSpotModalProps {
  onClose: () => void;
  onSubmit: (spot: any) => Promise<void>;
}

export function CreateTouristSpotModal({ onClose, onSubmit }: CreateTouristSpotModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    address: '',
    latitude: 0,
    longitude: 0,
    points_reward: 50,
    check_in_radius: 100,
    image_url: '',
    is_active: true,
    category: 'tourist_spot' as TouristSpotCategory,
    spots_whatsapp: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isSubmitting) return; // Prevent double submission
    
    setIsSubmitting(true);

    try {
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error('Erro ao criar ponto turístico:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const categoryOptions = [
    { value: 'tourist_spot', label: 'Ponto Turístico', icon: MapPin, color: 'text-blue-600' },
    { value: 'restaurant', label: 'Restaurante', icon: Utensils, color: 'text-orange-600' },
    { value: 'rental_services', label: 'Locação de Passeios e Veículos', icon: Car, color: 'text-green-600' },
  ];

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 sm:p-6 overflow-y-auto"
      onClick={handleBackdropClick}
    >
      <div 
        onClick={(e) => e.stopPropagation()}
        className="w-full max-w-2xl max-h-[90vh] sm:max-h-[85vh] my-4 flex flex-col bg-white rounded-lg shadow-xl"
      >
        <Card className="w-full h-full flex flex-col max-h-[95vh] overflow-auto">
        <CardHeader className="flex flex-row items-center justify-between shrink-0 px-4 sm:px-6 py-4 border-b">
          <h2 className="text-lg sm:text-xl font-semibold flex items-center">
            <MapPin className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
            <span className="hidden sm:inline">Novo Ponto Turístico</span>
            <span className="sm:hidden">Novo Ponto</span>
          </h2>
          <button 
            onClick={onClose} 
            className="text-gray-500 hover:text-gray-700 p-1 rounded-md hover:bg-gray-100 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </CardHeader>
        <CardContent className="flex-1 overflow-y-auto px-4 sm:px-6 py-4 min-h-0 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
          <div className="h-full">
            <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6 pb-8">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome do Local
              </label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ex: Parque Ibirapuera"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Descrição
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Descreva o ponto turístico..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none"
                rows={3}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Categoria
              </label>
              <div className="grid grid-cols-1 gap-2 sm:gap-3">
                {categoryOptions.map((option) => {
                  const IconComponent = option.icon;
                  return (
                    <label
                      key={option.value}
                      className={`flex items-center p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        formData.category === option.value
                          ? 'border-primary-500 bg-primary-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <input
                        type="radio"
                        name="category"
                        value={option.value}
                        checked={formData.category === option.value}
                        onChange={(e) => setFormData({ ...formData, category: e.target.value as TouristSpotCategory })}
                        className="sr-only"
                      />
                      <IconComponent className={`w-5 h-5 ${option.color} mr-3 shrink-0`} />
                      <span className="text-sm font-medium text-gray-700 leading-tight">{option.label}</span>
                    </label>
                  );
                })}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Endereço
              </label>
              <Input
                type="text"
                value={formData.address}
                onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                placeholder="Rua, número, bairro, cidade"
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pontos de Recompensa
                </label>
                <Input
                  type="number"
                  min="1"
                  value={String(formData.points_reward || '')}
                  onChange={(e) => setFormData({ ...formData, points_reward: parseInt(e.target.value) || 0 })}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Whatsapp
                </label>
                <Input
                  type="tel"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  value={formData.spots_whatsapp}
                  onChange={(e) =>setFormData({...formData,spots_whatsapp: e.target.value.replace(/\D/g, ""),})}
                  placeholder="Ex: 5587999999999"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Raio de Check-in (metros)
                </label>
                <Input
                  type="number"
                  min="50"
                  max="500"
                  value={String(formData.check_in_radius || '')}
                  onChange={(e) => setFormData({ ...formData, check_in_radius: parseInt(e.target.value) || 0 })}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Latitude
                </label>
                <Input
                  type="number"
                  step="0.000001"
                  value={String(formData.latitude || '')}
                  onChange={(e) => setFormData({ ...formData, latitude: parseFloat(e.target.value) || 0 })}
                  placeholder="-23.550520"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Longitude
                </label>
                <Input
                  type="number"
                  step="0.000001"
                  value={String(formData.longitude || '')}
                  onChange={(e) => setFormData({ ...formData, longitude: parseFloat(e.target.value) || 0 })}
                  placeholder="-46.633308"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                URL da Imagem
              </label>
              <Input
                type="url"
                value={formData.image_url}
                onChange={(e) => setFormData({ ...formData, image_url: e.target.value })}
                placeholder="https://exemplo.com/imagem.jpg"
              />
              <p className="text-xs text-gray-500 mt-1">
                Opcional: Adicione uma imagem representativa do local
              </p>
            </div>

            <div className="flex items-start">
              <label className="flex items-start cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.is_active}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                  className="mr-3 mt-0.5 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700 leading-5">Ativar ponto turístico imediatamente</span>
              </label>
            </div>

            <div className="flex flex-col sm:flex-row sm:justify-end gap-3 pt-4 sm:pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
                className="w-full sm:w-auto order-2 sm:order-1"
              >
                Cancelar
              </Button>
              <Button 
                type="submit" 
                disabled={isSubmitting}
                className="w-full sm:w-auto order-1 sm:order-2"
              >
                {isSubmitting ? 'Criando...' : 'Criar Ponto Turístico'}
              </Button>
            </div>
            </form>
          </div>
        </CardContent>
        </Card>
      </div>
    </div>
  );
}