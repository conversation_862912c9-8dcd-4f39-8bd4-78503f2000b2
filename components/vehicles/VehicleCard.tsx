'use client';

import { useState } from 'react';
import { 
  MapPin, 
  Clock, 
  Star, 
  MessageCircle, 
  Battery, 
  Zap,
  Bike,
  Car,
} from 'lucide-react';
import { Vehicle } from '@/types';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { ArrowRight } from 'lucide-react';


interface VehicleCardProps {
  vehicle: Vehicle;
  onRent: (vehicle: Vehicle, duration: string) => void;
  showRentButton?: boolean;
}

export function VehicleCard({ vehicle, onRent, showRentButton = true }: VehicleCardProps) {
  const [selectedDuration, setSelectedDuration] = useState('1 hora');

  const getVehicleIcon = (type: Vehicle['type']) => {
    const iconMap = {
      'scooter': Zap,
      'bike': Bike,
      'e-bike': Battery,
      'skateboard': Car,
    };
    return iconMap[type] || Bike;
  };

  const getTypeLabel = (type: Vehicle['type']) => {
    const labelMap = {
      'scooter': 'Patinete',
      'bike': 'Bicicleta',
      'e-bike': 'E-bike',
      'skateboard': 'Skate',
    };
    return labelMap[type] || type;
  };

  const formatFeatures = (features: any) => {
    if (!features) return [];
    
    try {
      const featuresObj = typeof features === 'string' ? JSON.parse(features) : features;
      return Object.entries(featuresObj).slice(0, 3).map(([key, value]) => ({
        key,
        value: String(value),
        label: formatFeatureLabel(key),
      }));
    } catch {
      return [];
    }
  };

  const formatFeatureLabel = (key: string) => {
    const labelMap: Record<string, string> = {
      'max_speed': 'Vel. máx',
      'autonomy': 'Autonomia',
      'battery_range': 'Autonomia',
      'weight': 'Peso',
      'gears': 'Marchas',
      'power': 'Potência',
      'length': 'Comprimento',
    };
    return labelMap[key] || key;
  };

  const IconComponent = getVehicleIcon(vehicle.type);
  const typeLabel = getTypeLabel(vehicle.type);
  const features = formatFeatures(vehicle.features);

  const durations = [
    '1 hora',
    '2 horas',
    '4 horas',
    'Dia inteiro',
  ];

  const handleRent = () => {
    onRent(vehicle, selectedDuration);
  };

  const handleLocation = (vehicle: any) => {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${vehicle.latitude},${vehicle.longitude}`;
    window.open(url, '_blank');
  };

  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-200">
      <div className="relative">
        {vehicle.image_url ? (
          <img
            src={vehicle.image_url}
            alt={vehicle.name}
            className="w-full h-48 object-cover"
            onError={(e) => {
              (e.target as HTMLImageElement).style.display = 'none';
              (e.target as HTMLImageElement).nextElementSibling?.classList.remove('hidden');
            }}
          />
        ) : null}
        
        <div className="hidden w-full h-48 bg-gradient-to-br from-primary-100 to-secondary-100 flex items-center justify-center">
          <IconComponent className="w-16 h-16 text-primary-600" />
        </div>

        <div className="absolute top-3 left-3 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
          <IconComponent className="w-3 h-3" />
          <span>{typeLabel}</span>
        </div>

        <div className="absolute top-3 right-3 bg-primary-600 text-white px-2 py-1 rounded-full text-sm font-bold">
          R$ {vehicle.hourly_price.toFixed(2)}/h
        </div>

        {vehicle.daily_price && (
          <div className="absolute top-10 right-3 bg-secondary-600 text-white px-2 py-1 rounded-full text-xs">
            R$ {vehicle.daily_price.toFixed(2)}/dia
          </div>
        )}
      </div>

      <CardContent className="p-4">
        <div className="mb-3">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {vehicle.name}
          </h3>
          
          {vehicle.description && (
            <p className="text-gray-600 text-sm line-clamp-2">
              {vehicle.description}
            </p>
          )}
        </div>

        {vehicle.location && (
          <div className="flex items-center text-gray-500 text-sm mb-3">
            <MapPin className="w-4 h-4 mr-1 flex-shrink-0" />
            <span>{vehicle.location}</span>
          </div>
        )}

        {features.length > 0 && (
          <div className="mb-4">
            <div className="grid grid-cols-3 gap-2">
              {features.map((feature, index) => (
                <div key={index} className="text-center p-2 bg-gray-50 rounded-lg">
                  <p className="text-xs text-gray-500">{feature.label}</p>
                  <p className="text-sm font-medium text-gray-900">{feature.value}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {showRentButton && (
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Duração do aluguel
              </label>
              <select
                value={selectedDuration}
                onChange={(e) => setSelectedDuration(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                {durations.map((duration) => (
                  <option key={duration} value={duration}>
                    {duration}
                  </option>
                ))}
              </select>
            </div>

            <Button
              onClick={() => handleLocation(vehicle)}
              variant="ghost"
              className="w-full bg-white text-blue-600 font-medium py-3 mb-5 shadow-md hover:bg-gray-100 transition-all duration-300"
            >
              <MapPin className="w-4 h-4 mr-2" />
              Acessar localização no mapa
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>

            <Button 
              onClick={handleRent}
              className="w-full"
              size="lg"
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Alugar via WhatsApp
            </Button>
          </div>
        )}

        <div className="mt-3 pt-3 border-t border-gray-100 flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center">
            <Star className="w-3 h-3 mr-1 fill-current text-yellow-400" />
            <span>Disponível</span>
          </div>
          <div className="flex items-center">
            <Clock className="w-3 h-3 mr-1" />
            <span>Retirada imediata</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}