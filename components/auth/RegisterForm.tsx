'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Eye, EyeOff } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { IMaskInput} from "react-imask";



function validarCPF(cpf: string): boolean {
  cpf = cpf.replace(/\D/g, "");
  if (cpf.length !== 11) return false;
  if (/^(\d)\1+$/.test(cpf)) return false;

  let soma = 0;
  for (let i = 0; i < 9; i++) soma += parseInt(cpf.charAt(i)) * (10 - i);
  let resto = (soma * 10) % 11;
  if (resto === 10 || resto === 11) resto = 0;
  if (resto !== parseInt(cpf.charAt(9))) return false;

  soma = 0;
  for (let i = 0; i < 10; i++) soma += parseInt(cpf.charAt(i)) * (11 - i);
  resto = (soma * 10) % 11;
  if (resto === 10 || resto === 11) resto = 0;
  if (resto !== parseInt(cpf.charAt(10))) return false;

  return true;
}


export function RegisterForm() {
  const [formData, setFormData] = useState<{
  fullName: string;
  email: string;
  phone: string;           // valor “puro” para enviar ao banco
  phoneFormatted: string;  // valor exibido com máscara
  password: string;
  confirmPassword: string;
  cpf: string;
}>({
  fullName: "",
  email: "",
  phone: "",
  phoneFormatted: "", // inicializa vazio
  password: "",
  confirmPassword: "",
  cpf: "",
});

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [erroCpf, setErroCpf] = useState<string | null>(null);

  
  const { signUp } = useAuth();
  const router = useRouter();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const validateForm = () => {
    const { fullName, email, phone, password, confirmPassword, cpf } = formData;

    if (!fullName || !email || !phone || !password || !confirmPassword) {
      return 'Preencha todos os campos';
    }

    if (fullName.trim().length < 2) {
      return 'Nome deve ter pelo menos 2 caracteres';
    }

    if (!email.includes('@')) {
      return 'Digite um email válido';
    }

    // Brazilian phone validation (basic)
    const phoneRegex = /^(\+55\s?)?(\(?\d{2}\)?\s?)?\d{4,5}-?\d{4}$/;
    if (!phoneRegex.test(phone.replace(/\s/g, ''))) {
      return 'Digite um número de telefone válido';
    }
    // if (!validarCPF(formData.cpf)) {
    //   return "Digite um CPF válido";
    // }


    if (password.length < 6) {
      return 'A senha deve ter pelo menos 6 caracteres';
    }

    if (password !== confirmPassword) {
      return 'As senhas não coincidem';
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      setLoading(false);
      return;
    }

    const { error: authError } = await signUp(
      formData.email,
      formData.password,
      formData.fullName,
      formData.phone,
      // formData.cpf
    );
    
    if (authError) {
      setError(authError);
      setLoading(false);
      return;
    }

    // Show success message and redirect
    router.push('/success');

    if (!validarCPF(formData.cpf)) {
      // setErroCpf("Digite um CPF válido");
      return;
    } else {
      // setErroCpf(null);
    }

    try {
  // await api.post('/cadastro', formData);
} catch (err: any) {
  
  // setErro(getErrorMessage(err.message)); // Mantém sua lógica
}


  };

  const formatPhone = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '');
    
    // Format based on length
    if (digits.length <= 2) {
      return `(${digits}`;
    } else if (digits.length <= 6) {
      return `(${digits.slice(0, 2)}) ${digits.slice(2)}`;
    } else if (digits.length <= 10) {
      return `(${digits.slice(0, 2)}) ${digits.slice(2, 6)}-${digits.slice(6)}`;
    } else {
      return `(${digits.slice(0, 2)}) ${digits.slice(2, 7)}-${digits.slice(7, 11)}`;
    }
  };

const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  const rawValue = e.target.value.replace(/\D/g, ""); // só números
  const formatted = rawValue.length > 0 ? formatPhone(rawValue) : "";

  setFormData(prev => ({
    ...prev,
    phone: rawValue,
    phoneFormatted: formatted,
  }));
};





  return (
    <div className="w-full max-w-md space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900">Criar Conta</h1>
        <p className="mt-2 text-gray-600">
          Junte-se ao Ponto X e comece a ganhar recompensas
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="p-3 text-sm text-red-700 bg-red-50 border border-red-200 rounded-md">
            {error}
          </div>
        )}   

        <Input
          label="Nome Completo"
          name="fullName"
          value={formData.fullName}
          onChange={handleChange}
          placeholder="Seu nome completo"
          autoComplete="name"
          required
        />

        <Input
          label="Email"
          name="email"
          type="email"
          value={formData.email}
          onChange={handleChange}
          placeholder="<EMAIL>"
          autoComplete="email"
          required
        />

{/* 
       <IMaskInput
        mask="000.000.000-00"
        value={formData.cpf}
        onAccept={(value: any) => {
          const rawValue = value.replace(/\D/g, "");
          setFormData({ ...formData, cpf: rawValue });
        }}
        as={Input} // 👈 aqui dizemos para usar seu componente Input
        label="CPF"
        name="cpf"
        placeholder="000.000.000-00"
        autoComplete="cpf"
        required
      /> */}


        {erroCpf && <p className="text-red-500 text-sm">{erroCpf}</p>}

        <Input
          label="Telefone"
          name="phone"
          value={formData.phoneFormatted || ""}
          onChange={handlePhoneChange}
          placeholder="(11) 99999-9999"
          autoComplete="tel"
          maxLength={15}
          required
        />




        <div className="relative">
          <Input
            label="Senha"
            name="password"
            type={showPassword ? 'text' : 'password'}
            value={formData.password}
            onChange={handleChange}
            placeholder="Mínimo 6 caracteres"
            autoComplete="new-password"
            required
          />
          <button
            type="button"
            className="absolute right-2 top-8 text-gray-400 hover:text-gray-600"
            onClick={() => setShowPassword(!showPassword)}
            tabIndex={-1}
          >
            {showPassword ? (
              <EyeOff className="w-5 h-5" />
            ) : (
              <Eye className="w-5 h-5" />
            )}
          </button>
        </div>

        <div className="relative">
          <Input
            label="Confirmar Senha"
            name="confirmPassword"
            type={showConfirmPassword ? 'text' : 'password'}
            value={formData.confirmPassword}
            onChange={handleChange}
            placeholder="Digite a senha novamente"
            autoComplete="new-password"
            required
          />
          <button
            type="button"
            className="absolute right-2 top-8 text-gray-400 hover:text-gray-600"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            tabIndex={-1}
          >
            {showConfirmPassword ? (
              <EyeOff className="w-5 h-5" />
            ) : (
              <Eye className="w-5 h-5" />
            )}
          </button>
        </div>

        <Button
          type="submit"
          className="w-full"
          loading={loading}
          size="lg"
        >
          Criar Conta
        </Button>
      </form>

      <div className="text-center">
        <p className="text-sm text-gray-600">
          Já tem uma conta?{' '}
          <Link 
            href="/login" 
            className="font-medium text-primary-600 hover:text-primary-500"
          >
            Faça login
          </Link>
        </p>
      </div>

      <div className="text-xs text-gray-500 text-center">
        Ao criar uma conta, você concorda com nossos{' '}
        <Link href="/termos" className="text-primary-600 hover:text-primary-500">
          Termos de Uso
        </Link>{' '}
        e{' '}
        <Link href="/privacidade" className="text-primary-600 hover:text-primary-500">
          Política de Privacidade
        </Link>
      </div>
    </div>
  );
}