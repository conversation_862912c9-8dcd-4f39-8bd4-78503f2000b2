'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { 
  Home, 
  User, 
  Trophy, 
  MapPin, 
  Bike,
  Gift,
  LogOut,
  Menu,
  X,
  Star,
  Settings,
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/Button';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { profile, signOut } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: Home },
    { name: 'Perfil', href: '/dashboard/perfil', icon: User },
    { name: 'Conquistas', href: '/dashboard/conquistas', icon: Trophy },
    { name: '<PERSON><PERSON>', href: '/dashboard/pontos-turisticos', icon: MapPin },
    { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/veiculos', icon: Bike },
    { name: 'Recompensas', href: '/dashboard/recompensas', icon: Gift },
  ];

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/login');
      router.refresh();
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
      // Force redirect even if signOut fails
      router.push('/login');
      router.refresh();
    }
  };

  const isActive = (href: string) => pathname === href;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <div className="absolute inset-0 bg-gray-600 opacity-75" />
        </div>
      )}

      {/* Fixed Sidebar */}
      <div className={`
        fixed top-0 left-0 z-50 w-64 h-full bg-white shadow-xl border-r border-gray-200 transform transition-transform duration-300 ease-in-out
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        {/* Sidebar Header */}
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200 bg-white">

          <Link href="/">
            <div className="flex items-center space-x-3 cursor-pointer">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-600 to-primary-700 rounded-lg flex items-center justify-center shadow-sm">
                <span className="text-white font-bold text-sm">Trivvy</span>
              </div>
              <span className="text-lg font-semibold text-gray-900">Locadora</span>
            </div>
          </Link>

          <button
            className="lg:hidden p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Sidebar Content */}
        <div className="flex flex-col h-full">
          {/* User Profile Section */}
          {/* {profile && (
            <div className="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center shadow-sm">
                  <span className="text-white font-medium text-sm">
                    {(profile.full_name || 'U')[0].toUpperCase()}
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {profile.full_name || 'Usuário'}
                  </p>
                  <div className="flex items-center space-x-1">
                    <Star className="w-3 h-3 text-yellow-500 fill-current" />
                    <span className="text-xs text-gray-600 font-medium">
                      {profile.level} • {profile.points.toLocaleString('pt-BR')} pts
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )} */}

          {/* Navigation Menu */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {navigation.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.href);
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`
                    group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200
                    ${active
                      ? 'bg-primary-50 text-primary-700 border-l-4 border-primary-600 shadow-sm'
                      : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50 border-l-4 border-transparent hover:border-gray-300'
                    }
                  `}
                  onClick={() => setSidebarOpen(false)}
                >
                  <Icon className={`
                    mr-3 h-5 w-5 flex-shrink-0 transition-colors duration-200
                    ${active ? 'text-primary-600' : 'text-gray-500 group-hover:text-gray-700'}
                  `} />
                  <span className="truncate">{item.name}</span>
                </Link>
              );
            })}
          </nav>

          {/* Bottom Actions */}
          <div className="px-4 py-4 border-t border-gray-200 bg-gray-50 space-y-2">
            {profile?.role === 'admin' && (
              <Link
                href="/admin"
                className="group flex items-center px-3 py-2.5 text-sm font-medium text-gray-700 rounded-lg hover:text-gray-900 hover:bg-white transition-all duration-200 border border-transparent hover:border-gray-200 hover:shadow-sm"
              >
                <Settings className="mr-3 h-5 w-5 text-gray-500 group-hover:text-gray-700" />
                <span>Administração</span>
              </Link>
            )}
            
            <button
              onClick={handleSignOut}
              className="w-full group flex items-center px-3 py-2.5 text-sm font-medium text-red-600 rounded-lg hover:text-red-700 hover:bg-red-50 transition-all duration-200 border border-transparent hover:border-red-200"
            >
              <LogOut className="mr-3 h-5 w-5 text-red-500 group-hover:text-red-600" />
              <span>Sair</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="lg:ml-64">
        {/* Top Navigation Bar */}
        <div className="sticky top-0 z-30 bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between h-16 px-6">
            <button
              className="lg:hidden p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100"
              onClick={() => setSidebarOpen(true)}
            >
              <Menu className="w-6 h-6" />
            </button>

            <div className="flex items-center space-x-4">
              {profile && (
                <div className="hidden md:flex items-center space-x-4">
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {profile.full_name || 'Usuário'}
                    </p>
                    <p className="text-xs text-gray-500">
                      {profile.level} • {profile.points.toLocaleString('pt-BR')} pontos
                    </p>
                  </div>
                  <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center shadow-sm">
                    <span className="text-white text-sm font-medium">
                      {(profile.full_name || 'U')[0].toUpperCase()}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Page Content */}
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  );
}