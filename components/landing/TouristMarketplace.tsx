'use client';

import React from 'react';
import { usePublicTouristSpots } from '@/hooks/usePublicTouristSpots';
import { useAppSettings } from '@/hooks/useAppSettings';
import { whatsappService } from '@/lib/whatsapp';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { 
  MapPin, 
  Camera, 
  Star, 
  ArrowRight,
  Gift,
  Trophy,
  MessageCircle,
  Sparkles,
  Navigation,
  Award
} from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

interface TouristMarketplaceProps {
  showHeader?: boolean;
  maxSpots?: number;
}

export function TouristMarketplace({ showHeader = true, maxSpots = 12 }: TouristMarketplaceProps) {
  const { touristSpots, loading } = usePublicTouristSpots();
  const { getWhatsAppNumber } = useAppSettings();

  const featuredSpots = touristSpots.slice(0, maxSpots);

  const handleDirectWhatsApp = (spot: any) => {
    const whatsappNumber = spot.spots_whatsapp ?? getWhatsAppNumber();
    const message = `Olá! Gostaria de saber mais sobre o ponto turístico ${spot.name}. Pode me ajudar com informações sobre como chegar?`;
    whatsappService.openWhatsApp(whatsappNumber, message);
  };

  const handleLocation = (spot: any) => {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${spot.latitude},${spot.longitude}`;
    window.open(url, '_blank');
  };

  if (loading) {
    return (
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="flex justify-center">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-gradient-to-br from-cyan-50 via-white to-blue-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMwMDAwMDAiIGZpbGwtb3BhY2l0eT0iMC4xIj48Y2lyY2xlIGN4PSIzMCIgY3k9IjMwIiByPSIyIi8+PC9nPjwvZz48L3N2Zz4=')] repeat"></div>
      </div>

      <div className="container mx-auto px-4 relative">
        {showHeader && (
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-cyan-50 text-cyan-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <Navigation className="w-4 h-4" />
              Pontos Turísticos
            </div>
                     
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Conheça{' '}
              <span className="text-cyan-600 relative">
                Pontos Turísticos
                <div className="absolute -bottom-2 left-0 right-0 h-3 bg-cyan-200 -z-10 rounded-full"></div>
              </span>
            </h2>
        </div>
        )}

        {/* Tourist Spots Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredSpots.map((spot) => {
            return (
              <Card 
                key={spot.id} 
                className="group hover:shadow-2xl transition-all duration-500 border-0 bg-white/80 backdrop-blur-sm hover:bg-white hover:scale-105 overflow-hidden"
              >
                <CardContent className="p-0">
                  {/* Spot Image */}
                  <div className="relative h-48 bg-gradient-to-br from-cyan-100 to-blue-200 flex items-center justify-center overflow-hidden">
                    {spot.image_url ? (
                      <Image
                        src={spot.image_url}
                        alt={spot.name}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-500"
                      />
                    ) : (
                      <div className="text-6xl opacity-80">📍</div>
                    )}
                    
                    {/* Points Badge */}
                    <div className="absolute top-4 left-4">
                      <Badge variant="secondary" className="bg-white/90 text-gray-700 backdrop-blur-sm">
                        <Trophy className="w-3 h-3 mr-1" />
                        +{spot.points_reward} pontos
                      </Badge>
                    </div>

                    {/* Check-in Available */}
                    <div className="absolute top-4 right-4">
                      <Badge variant="success" className="bg-cyan-500 text-white">
                        <Camera className="w-3 h-3 mr-1" />
                        Check-in
                      </Badge>
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 group-hover:text-cyan-600 transition-colors">
                          {spot.name}
                        </h3>
                        <div className="flex items-center gap-1 text-gray-500 text-sm mt-1">
                          <MapPin className="w-3 h-3" />
                          <span>{spot.address}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-cyan-600">
                          +{spot.points_reward}
                        </div>
                        <div className="text-sm text-gray-500">pontos</div>
                      </div>
                    </div>

                    {spot.description && (
                      <p className="text-gray-600 text-sm mb-6 line-clamp-2">
                        {spot.description}
                      </p>
                    )}

                    {/* Action Buttons */}
                    <div className="space-y-3">
                      {/* WhatsApp Direct */}
                      <Button
                        onClick={() => handleDirectWhatsApp(spot)}
                        className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 transition-all duration-300"
                      >
                        <MessageCircle className="w-4 h-4 mr-2" />
                        Pedir Informações
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>

                      <Button
                        onClick={() => handleLocation(spot)}
                        variant="ghost"
                        className="w-full bg-white text-blue-600 font-medium py-3 mt-5 shadow-md hover:bg-gray-100 transition-all duration-300"
                      >
                        <MapPin className="w-4 h-4 mr-2" />
                        Acessar localização no mapa
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>

                      {/* Gamification CTA */}
                      <Link href="/cadastro">
                        <Button
                          variant="outline"
                          className="w-full border-cyan-200 text-cyan-600 hover:bg-cyan-50 hover:border-cyan-300 font-medium py-3 transition-all duration-300 group mt-5"
                        >
                          <Gift className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                          Cadastrar e Fazer Check-in
                          <Award className="w-4 h-4 ml-2 group-hover:scale-110 transition-transform" />
                        </Button>
                      </Link>
                    </div>

                    {/* Benefits Preview */}
                    <div className="mt-4 p-3 bg-cyan-50 rounded-lg border border-cyan-100">
                      <div className="text-xs text-cyan-700 font-medium mb-1">
                         Com cadastro você ganha:
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-xs text-cyan-600">
                        <div>{/*spot.points_reward*/} pontos por check-in</div>
                        <div>Conquistas especiais</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

      </div>
    </section>
  );
}