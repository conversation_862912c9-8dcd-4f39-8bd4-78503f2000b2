import { RegisterForm } from '@/components/auth/RegisterForm';
import { LandingHeader } from '@/components/layout/LandingHeader';

export const metadata = {
  title: 'Cadastrar - Trivvy',
  description: 'Crie sua conta na Trivvy e comece a ganhar pontos no sistema Ponto X',
};

export default function RegisterPage() {
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-primary-50 to-secondary-50">
      {/* Header no topo, fora do container centralizado */}
      <div className="sticky top-0 z-50">
        <LandingHeader />
      </div>

      {/* Conteúdo centralizado abaixo do header */}
      <main className="flex-1 flex items-center justify-center px-4 py-8">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <div className="w-16 h-16 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">Trivvy</span>
              </div>
              <span className="text-2xl font-bold text-gray-900">Locadora</span>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-xl p-8">
            <RegisterForm />
          </div>

          <div className="text-center mt-6">
            <p className="text-sm text-gray-600">
              Dados protegidos com criptografia SSL
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}