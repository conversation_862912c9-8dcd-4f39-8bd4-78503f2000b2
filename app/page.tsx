import Link from 'next/link';
import Image from 'next/image';
import { <PERSON><PERSON><PERSON>, Star, Gift, Trophy, Zap, Target, Sparkles, Crown, Gamepad2 } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { LandingHeader } from '@/components/layout/LandingHeader';
import { VehicleMarketplace } from '@/components/landing/VehicleMarketplace';
import { TouristMarketplace } from '@/components/landing/TouristMarketplace';

// Cache desabilitado para homepage
export const dynamic = 'force-dynamic';
export const revalidate = 0;

export default function HomePage() {
  // Schema markup for SEO
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Trivvy",
    "description": "A primeira plataforma de mobilidade urbana com sistema completo de gamificação do Brasil",
    "url": process.env.NEXT_PUBLIC_APP_URL || "https://trivvy.com",
    "logo": `${process.env.NEXT_PUBLIC_APP_URL || "https://trivvy.com"}/assets/logo-2.png`,
    "foundingDate": "2025",
    "founder": {
      "@type": "Person",
      "name": "Trivvy"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": process.env.NEXT_PUBLIC_WHATSAPP_NUMBER,
      "contactType": "Customer Service",
      "availableLanguage": "Portuguese"
    },
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "BR",
      "addressLocality": "Brasil"
    },
    "sameAs": [
      "https://instagram.com/trivvy",
      "https://facebook.com/trivvy"
    ]
  };

  const webAppSchema = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Trivvy",
    "description": "Alugue patinetes, bikes e veículos recreativos enquanto ganha pontos e recompensas no sistema Ponto X",
    "url": process.env.NEXT_PUBLIC_APP_URL || "https://trivvy.com",
    "applicationCategory": "Mobility, Travel, Lifestyle",
    "operatingSystem": "Web Browser, iOS, Android",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "BRL",
      "description": "Cadastro gratuito com 100 pontos de bônus"
    },
    "featureList": [
      "Aluguel de veículos recreativos",
      "Sistema de gamificação com pontos",
      "Check-in em pontos turísticos",
      "Programa de recompensas",
      "5 níveis de usuário",
      "Descontos progressivos"
    ],
    "screenshot": `${process.env.NEXT_PUBLIC_APP_URL || "https://trivvy.com"}/screenshots/mobile-home.png`
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Schema Markup */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(webAppSchema)
        }}
      />

      {/* Header */}
      <LandingHeader />

      {/* Hero Section - Gradiente Limpo */}
      <main id="main-content">
        <section 
          className="relative min-h-screen flex items-center bg-gradient-to-br from-primary-500 via-primary-600 to-primary-800 overflow-hidden"
          aria-labelledby="hero-heading"
          role="banner"
        >
        {/* Gradiente Background */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-primary-400/30 via-primary-600/20 to-primary-800/40"></div>
          <div className="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4wNSI+PGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMiIvPjwvZz48L2c+PC9zdmc=')] opacity-30"></div>
        </div>

        <div className="container mx-auto px-4 py-10 lg:py-22 relative z-10">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
              {/* Left Column - Content */}
              <div className="text-center lg:text-left">
                {/* Trust Badge with Animation */}
                
                
                {/* Main Headline - Novo Copy */}
                <h1 
                  id="hero-heading"
                  className="text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 leading-none tracking-tight"
                  role="heading"
                  aria-level={1}
                >
                  Seu app de turismo, lazer e liberdade
                </h1>
                
                {/* Enhanced Value Proposition */}
                <p className="text-xl md:text-2xl text-white/90 mb-10 max-w-3xl leading-relaxed">
                  Explore, se divirta e economize com nossa plataforma de mobilidade gamificada.
                  <strong className="text-white"> Ganhe pontos</strong> e desbloqueie benefícios exclusivos.
                </p>
                
                {/* Modern Value Props with Icons */}
                {/* <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-12">
                  <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 hover:bg-white/20 transition-all duration-300 group">
                    <Zap className="w-8 h-8 text-yellow-300 mb-2 group-hover:scale-110 transition-transform" />
                    <div className="text-white font-bold text-lg">2min</div>
                    <div className="text-white/80 text-xs">Aluguel</div>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 hover:bg-white/20 transition-all duration-300 group">
                    <Trophy className="w-8 h-8 text-purple-300 mb-2 group-hover:scale-110 transition-transform" />
                    <div className="text-white font-bold text-lg">25%</div>
                    <div className="text-white/80 text-xs">Desconto</div>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 hover:bg-white/20 transition-all duration-300 group">
                    <Target className="w-8 h-8 text-cyan-300 mb-2 group-hover:scale-110 transition-transform" />
                    <div className="text-white font-bold text-lg">5+</div>
                    <div className="text-white/80 text-xs">Níveis</div>
                  </div>
                </div> */}
                
                {/* Enhanced CTA */}
                <div className="space-y-6">
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <Link 
                      href="/cadastro"
                      aria-label="Cadastrar grátis na Trivvy e começar a ganhar pontos"
                    >
                      <Button 
                        size="lg" 
                        className="bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white px-10 py-5 text-xl font-bold min-h-[64px] shadow-2xl hover:shadow-primary-500/25 transition-all duration-500 group relative overflow-hidden focus:ring-4 focus:ring-white/50 focus:outline-none"
                        role="button"
                        tabIndex={0}
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <Gift className="mr-3 w-6 h-6" aria-hidden="true" />
                        Começar Grátis
                        <ArrowRight className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform" aria-hidden="true" />
                      </Button>
                    </Link>
                  </div>
                  
                  {/* Social Proof */}
                  {/* <div className="flex items-center justify-center lg:justify-start gap-4 text-white/80">
                    <div className="flex -space-x-2">
                      <div className="w-8 h-8 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full border-2 border-white/20"></div>
                      <div className="w-8 h-8 bg-gradient-to-br from-green-400 to-green-600 rounded-full border-2 border-white/20"></div>
                      <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full border-2 border-white/20"></div>
                      <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full border-2 border-white/20 flex items-center justify-center text-xs font-bold text-white">+5K</div>
                    </div>
                    <div className="text-sm">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      </div>
                      <span>4.9/5.0 avaliação</span>
                    </div>
                  </div> */}
                </div>
              </div>

              {/* Right Column - Visual Elements */}
              <div className="relative lg:block hidden">
                {/* Floating Cards Animation */}
                <div className="relative w-full h-96">
                  {/* Main Phone Mockup */}
                  <div className="absolute top-0 right-0 w-64 h-80 bg-gradient-to-br from-white to-gray-100 rounded-3xl shadow-2xl border-8 border-white/20 backdrop-blur-sm transform rotate-6 hover:rotate-3 transition-transform duration-500">
                    <div className="p-6 space-y-4">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center">
                          <Crown className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <div className="font-bold text-gray-900">Nível Gold</div>
                          <div className="text-sm text-gray-500">1.847 pontos</div>
                        </div>
                      </div>
                      <div className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-2xl p-4">
                        <div className="text-sm text-primary-600 font-medium mb-1">Próximo aluguel</div>
                        <div className="text-2xl font-bold text-primary-700">10% OFF</div>
                      </div>
                      <div className="space-y-2">
                        <div className="bg-gray-100 rounded-lg p-3 flex items-center justify-between">
                          <span className="text-sm">🛴 Patinete Pro</span>
                          <span className="font-bold text-green-600">R$ 18</span>
                        </div>
                        <div className="bg-gray-100 rounded-lg p-3 flex items-center justify-between">
                          <span className="text-sm">🚲 Bike Elétrica</span>
                          <span className="font-bold text-green-600">R$ 25</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Floating Achievement Card */}
                  {/* <div className="absolute top-20 left-0 w-48 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl p-4 shadow-xl transform -rotate-12 hover:-rotate-6 transition-transform duration-500">
                    <div className="text-white">
                      <Trophy className="w-8 h-8 mb-2" />
                      <div className="font-bold">Conquista!</div>
                      <div className="text-sm opacity-90">+100 pontos ganhos</div>
                    </div>
                  </div> */}

                  {/* Floating Stats Card */}
                  {/* <div className="absolute bottom-0 left-10 w-40 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl p-4 shadow-xl transform rotate-12 hover:rotate-6 transition-transform duration-500">
                    <div className="text-white text-center">
                      <Gamepad2 className="w-8 h-8 mb-2 mx-auto" />
                      <div className="font-bold text-2xl">2.547</div>
                      <div className="text-sm opacity-90">Pontos Totais</div>
                    </div>
                  </div> */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      </main>

      {/* Vehicle Marketplace Section */}
      <section 
        id="veiculos" 
        aria-labelledby="vehicle-marketplace-heading"
        role="region"
      >
        <VehicleMarketplace />
      </section>

      {/* Tourist Spots Marketplace Section */}
      <section 
        id="pontos-turisticos" 
        aria-labelledby="tourist-spots-heading"
        role="region"
      >
        <TouristMarketplace />
      </section>

      {/* Recompensas Marketplace Section */}
      {/* <section className="py-20 bg-gradient-to-br from-pink-50 to-pink-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-pink-50 text-pink-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <Gift className="w-4 h-4" />
              Sistema de Recompensas
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Ganhe{' '}
              <span className="text-pink-600 relative">
                Recompensas Reais
                <div className="absolute -bottom-2 left-0 right-0 h-3 bg-pink-200 -z-10 rounded-full"></div>
              </span>
            </h2>
          </div>

          <div className="grid md:grid-cols-5 gap-4 mb-12">
            <div className="bg-white p-4 rounded-2xl shadow-sm text-center hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Trophy className="w-6 h-6 text-amber-600" />
              </div>
              <h3 className="font-bold text-gray-900 mb-1">Bronze</h3>
              <div className="text-2xl font-bold text-amber-600">0%</div>
              <p className="text-xs text-gray-500">0+ pontos</p>
            </div>
            <div className="bg-white p-4 rounded-2xl shadow-sm text-center hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Trophy className="w-6 h-6 text-gray-600" />
              </div>
              <h3 className="font-bold text-gray-900 mb-1">Silver</h3>
              <div className="text-2xl font-bold text-gray-600">5%</div>
              <p className="text-xs text-gray-500">500+ pontos</p>
            </div>
            <div className="bg-white p-4 rounded-2xl shadow-sm text-center hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Trophy className="w-6 h-6 text-yellow-600" />
              </div>
              <h3 className="font-bold text-gray-900 mb-1">Gold</h3>
              <div className="text-2xl font-bold text-yellow-600">10%</div>
              <p className="text-xs text-gray-500">1.500+ pontos</p>
            </div>
            <div className="bg-white p-4 rounded-2xl shadow-sm text-center hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Trophy className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-bold text-gray-900 mb-1">Platinum</h3>
              <div className="text-2xl font-bold text-purple-600">15%</div>
              <p className="text-xs text-gray-500">5.000+ pontos</p>
            </div>
            <div className="bg-white p-4 rounded-2xl shadow-sm text-center hover:shadow-lg transition-shadow relative">
              <div className="absolute -top-1 -right-1 bg-cyan-500 text-white px-2 py-1 rounded-full text-xs font-bold">VIP</div>
              <div className="w-12 h-12 bg-cyan-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Crown className="w-6 h-6 text-cyan-600" />
              </div>
              <h3 className="font-bold text-gray-900 mb-1">Diamond</h3>
              <div className="text-2xl font-bold text-cyan-600">25%</div>
              <p className="text-xs text-gray-500">15.000+ pontos</p>
            </div>
          </div>
        </div>
      </section> */}

      {/* How It Works Section */}
      <section className="py-20 bg-white" id="funcionamento">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Como Funciona? É Simples!
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Em apenas 3 passos você está pronto para se mover de forma inteligente pela cidade
            </p>
          </div>
          <div className="grid lg:grid-cols-3 gap-8 lg:gap-12">
            <div className="relative text-center lg:text-left">
              <div className="absolute -top-4 -left-4 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-lg lg:static lg:w-12 lg:h-12 lg:mb-6 lg:mx-auto lg:text-xl">
                1
              </div>
              <div className="bg-gradient-to-br from-primary-50 to-primary-100 p-8 rounded-2xl border border-primary-200">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                   Cadastre-se Grátis
                </h3>
                <p className="text-gray-600 text-lg leading-relaxed mb-4">
                  Crie sua conta em menos de 1 minuto e ganhe <strong>100 pontos</strong> para começar no sistema Ponto X.
                </p>
                {/* <div className="text-sm text-primary-600 font-medium">
                  🎮 100 pontos grátis • 🏆 Nível Bronze
                </div> */}
              </div>
            </div>
            <div className="relative text-center lg:text-left">
              <div className="absolute -top-4 -left-4 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-lg lg:static lg:w-12 lg:h-12 lg:mb-6 lg:mx-auto lg:text-xl">
                2
              </div>
              <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 p-8 rounded-2xl border border-yellow-200">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Escolha seu Veículo
                </h3>
                <p className="text-gray-600 text-lg leading-relaxed mb-4">
                  Patinetes, bikes ou carros. Veja disponibilidade e reserve via WhatsApp.
                </p>
                {/* <div className="text-sm text-primary-600 font-medium">
                  🛴 Patinetes • 🚲 Bikes • 🚗 Carros • 📱 WhatsApp direto
                </div> */}
              </div>
            </div>
            <div className="relative text-center lg:text-left">
              <div className="absolute -top-4 -left-4 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-lg lg:static lg:w-12 lg:h-12 lg:mb-6 lg:mx-auto lg:text-xl">
                3
              </div>
              <div className="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl border border-green-200">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Rode e Ganhe Pontos
                </h3>
                <p className="text-gray-600 text-lg leading-relaxed mb-4">
                  A cada viagem, check-in turístico e conquista você ganha pontos. Troque por descontos e prêmios.
                </p>
                {/* <div className="text-sm text-primary-600 font-medium">
                  🎯 +50 pontos por viagem • 📍 +100 por check-in • 🏆 Conquistas especiais
                </div> */}
              </div>
            </div>
          </div>
          <div className="text-center mt-12">
            <Link href="/cadastro">
              <Button 
                size="lg" 
                className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 text-lg font-semibold min-h-[56px] shadow-lg hover:shadow-xl transition-all duration-300"
              >
                Começar Agora - É Grátis!
                <ArrowRight className="ml-2 w-6 h-6" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      {/* <section className="py-20 bg-gradient-to-r from-primary-600 via-primary-700 to-primary-800 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="container mx-auto px-4 text-center relative">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Sua Mobilidade Inteligente Começa Agora
            </h2>
            <p className="text-xl text-primary-100 mb-8 max-w-3xl mx-auto leading-relaxed">
              Junte-se a <strong>mais de 10.000 brasileiros</strong> que já economizam dinheiro, ganham pontos e se movem de forma sustentável pela cidade
            </p>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 mb-8 max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-white mb-4">🎁 Oferta de Lançamento</h3>
              <div className="grid sm:grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-3xl font-bold text-white">100</div>
                  <div className="text-primary-100 text-sm">Pontos Grátis</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-white">25%</div>
                  <div className="text-primary-100 text-sm">Desconto Máximo</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-white">0%</div>
                  <div className="text-primary-100 text-sm">Taxa de Adesão</div>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <Link href="/cadastro">
                <Button 
                  variant="secondary" 
                  size="lg" 
                  className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 text-lg font-semibold min-h-[56px] shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Gift className="mr-3 w-7 h-7" />
                  Cadastrar Grátis e Ganhar Pontos
                  <ArrowRight className="ml-3 w-7 h-7" />
                </Button>
              </Link>
              
              <p className="text-primary-100 text-sm">
                ✓ Sem cartão de crédito • ✓ Ativação instantânea • ✓ Suporte 24/7
              </p>
            </div>
          </div>
        </div>
      </section> */}

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <Image
                  src="/assets/logo-2.png"
                  alt="Trivvy"
                  width={48}  
                  height={48}
                  className="rounded-lg"
                />
                <span className="text-2xl font-bold">Trivvy</span>
              </div>
              <p className="text-gray-300 text-lg mb-6 max-w-md">
                A primeira plataforma de mobilidade urbana com sistema completo de gamificação do Brasil.
              </p>
            </div>

            <div>
              <h3 className="font-bold text-lg mb-6 text-white">Produto</h3>
              <ul className="space-y-3 text-gray-300">
                <li><Link href="/como-funciona" className="hover:text-white transition-colors">Como Funciona</Link></li>
                <li><Link href="/veiculos" className="hover:text-white transition-colors">Veículos Disponíveis</Link></li>
                <li><Link href="/pontos-turisticos" className="hover:text-white transition-colors">Pontos Turísticos</Link></li>
                <li><Link href="/recompensas" className="hover:text-white transition-colors">Sistema de Recompensas</Link></li>
                <li><Link href="/niveis" className="hover:text-white transition-colors">Níveis Ponto X</Link></li>
              </ul>
            </div>

            <div>
              <h3 className="font-bold text-lg mb-6 text-white">Suporte & Legal</h3>
              <ul className="space-y-3 text-gray-300">
                <li><Link href="/ajuda" className="hover:text-white transition-colors">Central de Ajuda</Link></li>
                <li><Link href="/contato" className="hover:text-white transition-colors">Fale Conosco</Link></li>
                <li><Link href="/termos" className="hover:text-white transition-colors">Termos de Uso</Link></li>
                <li><Link href="/privacidade" className="hover:text-white transition-colors">Política de Privacidade</Link></li>
                <li><Link href="/cookies" className="hover:text-white transition-colors">Política de Cookies</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="text-center md:text-left">
                <p className="text-gray-400 text-sm">
                  &copy; 2025 Trivvy. Todos os direitos reservados.
                </p>
               
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}