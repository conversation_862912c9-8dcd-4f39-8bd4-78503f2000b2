'use client';

import { useState } from 'react';
import { useUsers } from '@/hooks/useUsers';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { EmptyState } from '@/components/ui/EmptyState';
import { ErrorState } from '@/components/ui/ErrorState';
import { useToast, ToastContainer } from '@/components/ui/Toast';
import { 
  Users, 
  Search,
  Filter,
  UserCheck,
  UserX,
  Edit,
  Shield,
  Crown,
  Star,
  TrendingUp,
  Calendar,
} from 'lucide-react';

export default function AdminUsersPage() {
  const { users, loading, error, updateUserRole, banUser, unbanUser } = useUsers();
  const { toasts, toast, removeToast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [selectedLevel, setSelectedLevel] = useState<string>('');
  const [showBanned, setShowBanned] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const filteredUsers = users.filter(user => {
    const matchesSearch = !searchQuery || 
      user.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesRole = !selectedRole || user.role === selectedRole;
    const matchesLevel = !selectedLevel || user.level === selectedLevel;
    const matchesBanned = showBanned || !user.is_banned;

    return matchesSearch && matchesRole && matchesLevel && matchesBanned;
  });

  const userStats = {
    total: users.length,
    active: users.filter(u => !u.is_banned).length,
    banned: users.filter(u => u.is_banned).length,
    admins: users.filter(u => u.role === 'admin').length,
    totalPoints: users.reduce((sum, u) => sum + u.total_points_earned, 0),
  };

  const handleRoleChange = async (userId: string, newRole: 'user' | 'admin') => {
    if (window.confirm(`Tem certeza que deseja alterar o papel deste usuário para ${newRole}?`)) {
      setActionLoading(userId);
      try {
        await updateUserRole(userId, newRole);
        toast.success(`Papel do usuário alterado para ${newRole === 'admin' ? 'administrador' : 'usuário'} com sucesso!`);
      } catch (error) {
        console.error('Erro ao alterar papel do usuário:', error);
        toast.error('Erro ao alterar papel do usuário. Tente novamente.');
      } finally {
        setActionLoading(null);
      }
    }
  };

  const handleBanToggle = async (userId: string, isBanned: boolean) => {
    const action = isBanned ? 'desbanir' : 'banir';
    if (window.confirm(`Tem certeza que deseja ${action} este usuário?`)) {
      setActionLoading(userId);
      try {
        if (isBanned) {
          await unbanUser(userId);
          toast.success('Usuário desbanido com sucesso!');
        } else {
          await banUser(userId);
          toast.success('Usuário banido com sucesso!');
        }
      } catch (error) {
        console.error(`Erro ao ${action} usuário:`, error);
        toast.error(`Erro ao ${action} usuário. Tente novamente.`);
      } finally {
        setActionLoading(null);
      }
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <Card>
          <CardContent className="p-8">
            <ErrorState 
              message={error}
              onRetry={() => window.location.reload()}
            />
          </CardContent>
        </Card>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gerenciar Usuários</h1>
            <p className="text-gray-600">
              Visualize e gerencie todos os usuários da plataforma
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <Users className="w-8 h-8 text-primary-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{userStats.total}</div>
              <div className="text-sm text-gray-600">Total de Usuários</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <UserCheck className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{userStats.active}</div>
              <div className="text-sm text-gray-600">Usuários Ativos</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <UserX className="w-8 h-8 text-red-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{userStats.banned}</div>
              <div className="text-sm text-gray-600">Usuários Banidos</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Crown className="w-8 h-8 text-gold mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{userStats.admins}</div>
              <div className="text-sm text-gray-600">Administradores</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Star className="w-8 h-8 text-secondary-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900">{userStats.totalPoints.toLocaleString()}</div>
              <div className="text-sm text-gray-600">Pontos Totais</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    type="text"
                    placeholder="Buscar usuários por nome ou email..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <select
                  value={selectedRole}
                  onChange={(e) => setSelectedRole(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Todos os Papéis</option>
                  <option value="user">Usuário</option>
                  <option value="admin">Administrador</option>
                </select>

                <select
                  value={selectedLevel}
                  onChange={(e) => setSelectedLevel(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Todos os Níveis</option>
                  <option value="bronze">Bronze</option>
                  <option value="silver">Silver</option>
                  <option value="gold">Gold</option>
                  <option value="platinum">Platinum</option>
                  <option value="diamond">Diamond</option>
                </select>

                <Button
                  variant={showBanned ? "primary" : "outline"}
                  onClick={() => setShowBanned(!showBanned)}
                  className="whitespace-nowrap"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  {showBanned ? 'Todos' : 'Ativos'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Users Table */}
        {filteredUsers.length === 0 ? (
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={Users}
                title="Nenhum usuário encontrado"
                description="Tente ajustar os filtros de busca"
              />
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Usuário
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Nível
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Pontos
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Papel
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Cadastro
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ações
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredUsers.map((user) => (
                      <tr key={user.id} className={user.is_banned ? 'bg-red-50' : ''}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center mr-3">
                              <span className="text-white font-medium">
                                {(user.full_name || user.email)[0].toUpperCase()}
                              </span>
                            </div>
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {user.full_name || 'Nome não informado'}
                              </div>
                              <div className="text-sm text-gray-500">{user.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <Badge variant="outline" className="capitalize">
                            {user.level}
                          </Badge>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{user.points}</div>
                          <div className="text-xs text-gray-500">
                            Total: {user.total_points_earned}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <Badge variant={user.role === 'admin' ? 'success' : 'secondary'}>
                            {user.role === 'admin' ? 'Admin' : 'Usuário'}
                          </Badge>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <Badge variant={user.is_banned ? 'danger' : 'success'}>
                            {user.is_banned ? 'Banido' : 'Ativo'}
                          </Badge>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(user.created_at).toLocaleDateString('pt-BR')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRoleChange(
                                user.id, 
                                user.role === 'admin' ? 'user' : 'admin'
                              )}
                              disabled={actionLoading === user.id}
                              className="text-primary-600 hover:text-primary-900"
                            >
                              {actionLoading === user.id ? (
                                <LoadingSpinner size="sm" />
                              ) : (
                                <Shield className="w-4 h-4" />
                              )}
                            </Button>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleBanToggle(user.id, user.is_banned)}
                              disabled={actionLoading === user.id}
                              className={user.is_banned ? 
                                "text-green-600 hover:text-green-900" : 
                                "text-red-600 hover:text-red-900"
                              }
                            >
                              {actionLoading === user.id ? (
                                <LoadingSpinner size="sm" />
                              ) : user.is_banned ? (
                                <UserCheck className="w-4 h-4" />
                              ) : (
                                <UserX className="w-4 h-4" />
                              )}
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <TrendingUp className="w-5 h-5 mr-2" />
              Usuários Recentes
            </h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {users
                .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
                .slice(0, 5)
                .map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {(user.full_name || user.email)[0].toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">
                          {user.full_name || 'Nome não informado'}
                        </h4>
                        <p className="text-sm text-gray-600">{user.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" size="sm">
                        {user.level}
                      </Badge>
                      <span className="text-sm text-gray-500">
                        <Calendar className="w-4 h-4 inline mr-1" />
                        {new Date(user.created_at).toLocaleDateString('pt-BR')}
                      </span>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </AdminLayout>
  );
}