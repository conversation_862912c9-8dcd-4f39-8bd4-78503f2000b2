import { LoginForm } from '@/components/auth/LoginForm';
import { LandingHeader } from '@/components/layout/LandingHeader';

export const metadata = {
  title: 'Entrar - Trivvy',
  description: 'Entre na sua conta Trivvy e continue ganhando pontos no sistema Ponto X',
};

export default function LoginPage() {
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-primary-50 to-secondary-50">
      
      {/* Navbar no topo */}
      <div className="sticky top-0 z-50">
        <LandingHeader />
      </div>

      {/* Conteúdo centralizado */}
      <main className="flex-1 flex items-center justify-center px-4 py-8">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <div className="w-16 h-16 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">Trivvy</span>
              </div>
              <span className="text-2xl font-bold text-gray-900">Locadora</span>
            </div>
          </div>
          
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <LoginForm />
          </div>
          
          <div className="text-center mt-6">
            <p className="text-sm text-gray-600">
              Protegido por criptografia de ponta a ponta
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
