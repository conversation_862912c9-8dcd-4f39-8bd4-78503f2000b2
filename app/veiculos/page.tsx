'use client';

import { useState, useMemo } from 'react';
import { Vehicle } from '@/types';
import { useVehicles } from '@/hooks/useVehicles';
import { useAuth } from '@/hooks/useAuth';
import { useAppSettings } from '@/hooks/useAppSettings';
import { whatsappService } from '@/lib/whatsapp';
import { VehicleCard } from '@/components/vehicles/VehicleCard';
import { VehicleFilter } from '@/components/vehicles/VehicleFilter';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { ArrowLeft, MapPin } from 'lucide-react';
import Link from 'next/link';

interface VehicleFilters {
  type?: Vehicle['type'];
  maxPrice?: number;
  location?: string;
  searchQuery?: string;
}

export default function VehiclesPage() {
  const { vehicles, loading, error, getVehicleTypes, getAvailableLocations, getPriceRange } = useVehicles();
  const { profile } = useAuth();
  const { getWhatsAppNumber } = useAppSettings();
  const [filters, setFilters] = useState<VehicleFilters>({});

  const vehicleTypes = getVehicleTypes();
  const availableLocations = getAvailableLocations();
  const priceRange = getPriceRange();

  const filteredVehicles = useMemo(() => {
    let filtered = vehicles;

    // Search filter
    if (filters.searchQuery?.trim()) {
      const query = filters.searchQuery.toLowerCase();
      filtered = filtered.filter(vehicle =>
        vehicle.name.toLowerCase().includes(query) ||
        vehicle.description?.toLowerCase().includes(query) ||
        vehicle.location?.toLowerCase().includes(query)
      );
    }

    // Type filter
    if (filters.type) {
      filtered = filtered.filter(vehicle => vehicle.type === filters.type);
    }

    // Price filter
    if (filters.maxPrice) {
      filtered = filtered.filter(vehicle => vehicle.hourly_price <= filters.maxPrice!);
    }

    // Location filter
    if (filters.location) {
      filtered = filtered.filter(vehicle => vehicle.location === filters.location);
    }

    return filtered;
  }, [vehicles, filters]);

  const handleRent = (vehicle: Vehicle, duration: string) => {
    if (!profile) {
      // Redirect to login if not authenticated
      window.location.href = '/login?redirect=/veiculos';
      return;
    }

    try {
      const whatsappNumber = vehicle.vehicle_whatsapp ?? getWhatsAppNumber();
      
      if (!whatsappNumber) {
        alert('Erro: Número do WhatsApp não configurado. Entre em contato com o suporte.');
        return;
      }
      
      const message = whatsappService.generateRentalMessage(vehicle, profile, duration);
      whatsappService.openWhatsApp(whatsappNumber, message);
    } catch (error) {
      console.error('Erro ao processar aluguel:', error);
      alert('Erro ao processar solicitação de aluguel. Tente novamente.');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Carregando veículos...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <Card>
            <CardContent className="text-center p-8">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>
                Tentar Novamente
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Mobile-first */}
      <div className="bg-gradient-to-r from-white via-primary-50 to-white shadow-sm border-b border-primary-100">
        <div className="container mx-auto px-4 py-3 md:py-4">
          <div className="flex flex-col space-y-3 md:space-y-0 md:flex-row md:items-center md:justify-between">
            {/* Navigation and Title */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 md:space-x-4">
                <Link href="/dashboard">
                  <Button variant="ghost" size="sm" className="min-h-[40px] px-2 md:px-3">
                    <ArrowLeft className="w-4 h-4 mr-1 md:mr-2" />
                    <span className="hidden sm:inline">Voltar</span>
                  </Button>
                </Link>
                <div className="flex-1 min-w-0">
                  <h4 className="text-xl md:text-2xl font-bold text-gray-900 truncate">
                    Veículos Disponíveis
                  </h4>
                  <p className="text-xs md:text-sm text-gray-600 mt-0.5">
                    {filteredVehicles.length} veículo{filteredVehicles.length !== 1 ? 's' : ''} disponível{filteredVehicles.length !== 1 ? 'eis' : ''}
                  </p>
                </div>
              </div>

              {/* Mobile profile avatar only */}
              {profile && (
                <div className="md:hidden">
                  <div className="w-9 h-9 bg-primary-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {(profile.full_name || 'U')[0].toUpperCase()}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Desktop profile section */}
            {profile && (
              <div className="hidden md:flex items-center space-x-3 text-sm">
                <div className="text-right">
                  <p className="font-medium text-gray-900">
                    Olá, {profile.full_name || 'Usuário'}
                  </p>
                  <p className="text-xs text-primary-600">
                    {profile.total_points_earned || 0} pontos • Nível {profile.level || 'Bronze'}
                  </p>
                </div>
                <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">
                    {(profile.full_name || 'U')[0].toUpperCase()}
                  </span>
                </div>
              </div>
            )}

            {/* Mobile stats bar */}
            {profile && (
              <div className="md:hidden bg-primary-50 -mx-4 px-4 py-2 border-t border-primary-100">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-700">
                    <span className="font-medium">{profile.total_points_earned || 0}</span> pontos
                  </span>
                  <span className="text-primary-600 font-medium">
                    Nível {profile.level || 'Bronze'}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-4 md:py-8">
        {/* Filters - Mobile-optimized */}
        <div className="mb-6 md:mb-8">
          <VehicleFilter
            onFiltersChange={setFilters}
            availableLocations={availableLocations}
            priceRange={priceRange}
            vehicleTypes={vehicleTypes}
          />
        </div>

        {/* Results */}
        {filteredVehicles.length === 0 ? (
          <Card className="mx-2 md:mx-0">
            <CardContent className="text-center p-6 md:p-8">
              <MapPin className="w-10 h-10 md:w-12 md:h-12 text-gray-400 mx-auto mb-3 md:mb-4" />
              <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2">
                Nenhum veículo encontrado
              </h3>
              <p className="text-sm md:text-base text-gray-600 mb-4">
                Tente ajustar os filtros ou fazer uma nova busca
              </p>
              <Button 
                onClick={() => setFilters({})}
                className="min-h-[44px] px-6"
              >
                Limpar Filtros
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
            {filteredVehicles.map((vehicle) => (
              <VehicleCard
                key={vehicle.id}
                vehicle={vehicle}
                onRent={handleRent}
                showRentButton={true}
              />
            ))}
          </div>
        )}

        {/* Info Card - Mobile-optimized */}
        <Card className="mt-6 md:mt-8 mx-2 md:mx-0">
          <CardContent className="p-4 md:p-6">
            <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">
              💡 Como funciona o aluguel?
            </h3>
            <div className="space-y-2 md:space-y-3 text-xs md:text-sm text-gray-600">
              <div className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-5 h-5 bg-primary-100 text-primary-600 rounded-full text-xs flex items-center justify-center font-medium mt-0.5">1</span>
                <p>Escolha o veículo e duração desejados</p>
              </div>
              <div className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-5 h-5 bg-primary-100 text-primary-600 rounded-full text-xs flex items-center justify-center font-medium mt-0.5">2</span>
                <p>Clique em &quot;Alugar via WhatsApp&quot; para ser direcionado</p>
              </div>
              <div className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-5 h-5 bg-primary-100 text-primary-600 rounded-full text-xs flex items-center justify-center font-medium mt-0.5">3</span>
                <p>Confirme os detalhes pelo WhatsApp</p>
              </div>
              <div className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-5 h-5 bg-primary-100 text-primary-600 rounded-full text-xs flex items-center justify-center font-medium mt-0.5">4</span>
                <p>Retire o veículo no local informado</p>
              </div>
              <div className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-5 h-5 bg-primary-100 text-primary-600 rounded-full text-xs flex items-center justify-center font-medium mt-0.5">5</span>
                <p className="font-medium text-primary-700">Divirta-se e ganhe pontos no sistema Ponto X! 🎯</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}